rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Development environment - more permissive rules for testing
    
    // Personal queues - users can only access their own queues
    match /personal_queues/{queueId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // Unified queues collection - handles both public and private queues
    match /queues/{queueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // User profiles - users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // App configuration - allow read access for server-side API routes
    // Development: Allow more permissive access for testing
    match /app_config/{configId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Queue statistics - read-only for authenticated users
    match /stats/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // Allow writes in development
    }
    
    // Development-only: Allow admin access to all collections
    match /{document=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.email != null && 
        request.auth.token.email.matches('.*@yourdomain\\.com$');
    }
  }
}
