rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Personal queues - users can only access their own queues
    match /personal_queues/{queueId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // Unified queues collection - handles both public and private queues
    match /queues/{queueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // User profiles - users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // App configuration - allow read access for server-side API routes
    // Passwords are now hashed, so reading them is safe for verification
    match /app_config/{configId} {
      allow read: if true; // Allow read for server-side verification (passwords are hashed)
      allow write: if request.auth != null; // Only authenticated users can write
    }
    
    // Queue statistics - read-only for authenticated users
    match /stats/{document} {
      allow read: if request.auth != null;
      allow write: if false; // Only server-side functions can write stats
    }
  }
}
