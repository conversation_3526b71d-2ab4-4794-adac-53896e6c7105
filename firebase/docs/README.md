# Firebase Configuration and Deployment Guide

This directory contains all Firebase-related configuration files, scripts, and documentation for the project.

## Directory Structure

```
firebase/
├── config/                 # Firebase configuration files
│   ├── firebase.json              # Firebase CLI configuration
│   ├── .firebaserc               # Firebase project aliases
│   ├── firestore.rules           # Production Firestore security rules
│   ├── firestore.development.rules # Development Firestore rules (optional)
│   └── firestore.indexes.json    # Firestore database indexes
├── scripts/               # Firebase deployment and utility scripts
│   ├── deploy-firebase.js        # Main deployment script
│   └── setup-password.js         # Password setup utility
└── docs/                  # Firebase documentation
    ├── README.md                 # This file
    ├── deployment-guide.md       # Detailed deployment instructions
    └── security-rules.md         # Security rules documentation
```

## Quick Start

### 1. Environment Setup
```bash
# Copy environment template
npm run env:copy

# Edit your environment files with Firebase config
# .env.local (development)
# .env.production (production)
```

### 2. Firebase Project Setup
```bash
# Initialize Firebase CLI (if not already done)
firebase login

# Set up project aliases
cd firebase/config
firebase use --add  # Follow prompts to add your projects
```

### 3. Deploy to Environment
```bash
# Deploy to development
npm run deploy:dev

# Deploy to staging  
npm run deploy:staging

# Deploy to production
npm run deploy:prod
```

## Available Commands

### Deployment Commands
- `npm run deploy:dev` - Deploy everything to development
- `npm run deploy:staging` - Deploy everything to staging
- `npm run deploy:prod` - Deploy everything to production

### Granular Deployment
- `node firebase/scripts/deploy-firebase.js <env> <type>`
  - `<env>`: development, staging, production
  - `<type>`: all, hosting, firestore, rules, indexes

### Examples
```bash
# Deploy only hosting to production
node firebase/scripts/deploy-firebase.js production hosting

# Deploy only Firestore rules to development
node firebase/scripts/deploy-firebase.js development rules

# Deploy only indexes to staging
node firebase/scripts/deploy-firebase.js staging indexes
```

### Utility Commands
- `npm run firebase:emulators` - Start Firebase emulators
- `npm run setup:password` - Set up app password protection

## Environment Configuration

### Firebase Projects
- **Development**: `looper-dev-project-id`
- **Staging**: `looper-staging-project-id`
- **Production**: `looper-2630d`

### Required Environment Variables
```bash
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

## Security Rules

The project uses environment-specific Firestore security rules:
- `firestore.rules` - Default/production rules (strict)
- `firestore.development.rules` - Development rules (permissive for testing)

## Troubleshooting

### Common Issues
1. **Permission Denied**: Ensure you're logged into Firebase CLI and have access to the target project
2. **Project Not Found**: Check that project aliases are set up correctly in `.firebaserc`
3. **Build Failures**: Ensure environment variables are set correctly for the target environment

### Getting Help
- Check the deployment logs for specific error messages
- Verify Firebase project settings in the console
- Ensure all required environment variables are set

## Best Practices

1. **Always test in development first**
2. **Use staging for final testing before production**
3. **Deploy rules and indexes separately when making changes**
4. **Keep environment variables secure and never commit them**
5. **Use the deployment script for consistent deployments**
