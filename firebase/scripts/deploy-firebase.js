#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get the project root directory (two levels up from this script)
const projectRoot = path.resolve(__dirname, '../../');
const firebaseConfigDir = path.resolve(__dirname, '../config/');

// Parse command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'development';
const deployType = args[1] || 'all'; // all, hosting, firestore, rules, indexes

// Validate environment
const validEnvironments = ['development', 'staging', 'production'];
if (!validEnvironments.includes(environment)) {
  console.error(`❌ Invalid environment: ${environment}`);
  console.error(`Valid environments: ${validEnvironments.join(', ')}`);
  process.exit(1);
}

// Validate deploy type
const validDeployTypes = ['all', 'hosting', 'firestore', 'rules', 'indexes'];
if (!validDeployTypes.includes(deployType)) {
  console.error(`❌ Invalid deploy type: ${deployType}`);
  console.error(`Valid deploy types: ${validDeployTypes.join(', ')}`);
  process.exit(1);
}

console.log(`🚀 Deploying to ${environment} environment...`);
console.log(`📦 Deploy type: ${deployType}`);

try {
  // Check if environment-specific config exists
  const configFile = path.join(firebaseConfigDir, `firebase.${environment}.json`);
  const defaultConfigFile = path.join(firebaseConfigDir, 'firebase.json');

  if (!fs.existsSync(configFile)) {
    console.warn(`⚠️  No environment-specific config found: ${configFile}`);
    console.log(`Using default firebase.json`);
  }

  // Build the application for the target environment
  console.log(`🔨 Building application for ${environment}...`);
  if (environment === 'development') {
    execSync('npm run export:dev', { stdio: 'inherit', cwd: projectRoot });
  } else {
    execSync('npm run export:prod', { stdio: 'inherit', cwd: projectRoot });
  }

  // Construct Firebase deploy command
  let deployCommand = 'firebase deploy';

  // Add project flag
  deployCommand += ` --project ${environment}`;

  // Add config file if it exists
  if (fs.existsSync(configFile)) {
    deployCommand += ` --config firebase.${environment}.json`;
  }
  
  // Add deploy type flag
  if (deployType !== 'all') {
    if (deployType === 'firestore') {
      deployCommand += ' --only firestore';
    } else if (deployType === 'rules') {
      deployCommand += ' --only firestore:rules';
    } else if (deployType === 'indexes') {
      deployCommand += ' --only firestore:indexes';
    } else {
      deployCommand += ` --only ${deployType}`;
    }
  }

  console.log(`🚀 Running: ${deployCommand}`);
  execSync(deployCommand, { stdio: 'inherit', cwd: firebaseConfigDir });

  console.log(`✅ Successfully deployed to ${environment}!`);
  
  // Show helpful information
  if (environment === 'production') {
    console.log(`🌐 Production URL: https://your-domain.com`);
  } else if (environment === 'staging') {
    console.log(`🌐 Staging URL: https://staging-project-id.web.app`);
  } else {
    console.log(`🌐 Development URL: https://dev-project-id.web.app`);
  }

} catch (error) {
  console.error(`❌ Deployment failed:`, error.message);
  process.exit(1);
}
