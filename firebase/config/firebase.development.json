{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": ".next/out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}]}]}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "ui": {"enabled": true, "port": 4000}}}