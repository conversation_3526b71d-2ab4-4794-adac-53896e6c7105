# Firebase Configuration

This directory contains all Firebase-related files for the project, organized for easy management across multiple environments.

## Quick Start

```bash
# Deploy to development
npm run deploy:dev

# Deploy to production  
npm run deploy:prod

# Start Firebase emulators
npm run firebase:emulators
```

## Directory Structure

```
firebase/
├── config/                    # All Firebase configuration files
│   ├── firebase.json                 # Default Firebase configuration
│   ├── firebase.development.json     # Development environment config
│   ├── firebase.staging.json         # Staging environment config  
│   ├── firebase.production.json      # Production environment config
│   ├── .firebaserc                   # Firebase project aliases
│   ├── firestore.rules               # Production Firestore security rules
│   ├── firestore.development.rules   # Development Firestore rules
│   └── firestore.indexes.json        # Firestore database indexes
├── scripts/                   # Firebase deployment and utility scripts
│   ├── deploy-firebase.js            # Main deployment script
│   └── setup-password.js             # Password setup utility
└── docs/                      # Comprehensive Firebase documentation
    ├── README.md                     # Main Firebase documentation
    ├── deployment-guide.md           # Detailed deployment instructions
    └── security-rules.md             # Security rules documentation
```

## Documentation

- **[Main Documentation](docs/README.md)** - Complete Firebase setup and usage guide
- **[Deployment Guide](docs/deployment-guide.md)** - Detailed deployment workflows and procedures
- **[Security Rules](docs/security-rules.md)** - Firestore security rules documentation

## Environment Configuration

The project supports three environments:

- **Development**: `firebase.development.json` - Local development with emulators
- **Staging**: `firebase.staging.json` - Pre-production testing environment  
- **Production**: `firebase.production.json` - Live production environment

Each environment has its own Firebase project and configuration optimized for its use case.

## Key Features

- ✅ **Multi-environment support** with separate Firebase projects
- ✅ **Environment-specific configurations** for optimal performance
- ✅ **Automated deployment scripts** for consistent deployments
- ✅ **Granular deployment options** (hosting, rules, indexes separately)
- ✅ **Security rules** tailored for each environment
- ✅ **Comprehensive documentation** for all Firebase operations
- ✅ **Firebase emulator support** for local development

## Getting Started

1. **Set up environment variables** in `.env.local` and `.env.production`
2. **Configure Firebase projects** using `firebase use --add` in the `config/` directory
3. **Deploy to development** with `npm run deploy:dev`
4. **Test and deploy to production** with `npm run deploy:prod`

For detailed instructions, see the [documentation](docs/README.md).
